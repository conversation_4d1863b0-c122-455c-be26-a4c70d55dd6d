#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
释永信（独家视频号）稳过爆单处理器
功能标识: wolverine_sph
基于OpenCV处理画面，FFmpeg双DAR修正并保留音频
"""

import os
import cv2
import numpy as np
import subprocess
import tempfile
import shutil
from pathlib import Path
from PyQt5.QtCore import QThread, pyqtSignal

class WolverineProcessor(QThread):
    """释永信（独家视频号）稳过爆单处理器"""

    progress_updated = pyqtSignal(int, str)
    process_finished = pyqtSignal(bool, str)

    def __init__(self, ffmpeg_path, a_video_list, b_video_list, output_dir, config_code, delete_used_b=False):
        super().__init__()
        self.ffmpeg_path = ffmpeg_path
        self.a_video_list = a_video_list
        self.b_video_list = b_video_list
        self.output_dir = output_dir
        self.config_code = config_code
        self.delete_used_b = delete_used_b
        self.is_cancelled = False
        self.current_process = None
        self.temp_dir = None
        self.used_b_videos = set()

        # ========= 基本配置 =========
        self.FEATHER = 10
        self.FPS = 30
        self.SIZE_BG = 1080
        self.SIZE_OUT = (1080, 1920)

        print("[释永信] 处理器初始化完成")

    def make_mask(self, h, w, feather=10):
        """创建羽化遮罩"""
        mask = np.ones((h, w), np.uint8) * 255
        mask[:feather, :] = mask[-feather:, :] = 0
        mask[:, :feather] = mask[:, -feather:] = 0
        return cv2.GaussianBlur(mask, (feather*2+1, feather*2+1), 0)

    def resize_and_crop(self, frame, target):
        """调整大小并裁剪"""
        h, w = frame.shape[:2]
        scale = target / min(h, w)
        new_w, new_h = int(w * scale), int(h * scale)
        frame = cv2.resize(frame, (new_w, new_h), interpolation=cv2.INTER_LINEAR)
        x0, y0 = (new_w - target)//2, (new_h - target)//2
        return frame[y0:y0+target, x0:x0+target]

    def cancel(self):
        """取消处理"""
        self.is_cancelled = True
        if self.current_process:
            try:
                self.current_process.terminate()
            except:
                pass
        print("[释永信] 处理已取消")

    def cleanup_temp_files(self):
        """清理临时文件"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            try:
                shutil.rmtree(self.temp_dir)
                print("[释永信] 临时文件清理完成")
            except Exception as e:
                print(f"[释永信] 清理临时文件失败: {e}")

    def run(self):
        """主处理流程"""
        try:
            if not self.a_video_list:
                self.process_finished.emit(False, "A视频列表为空")
                return

            if not self.b_video_list:
                self.process_finished.emit(False, "B视频列表为空")
                return

            # 创建临时目录
            self.temp_dir = tempfile.mkdtemp(prefix="wolverine_")
            print(f"[释永信] 创建临时目录: {self.temp_dir}")

            total_videos = len(self.a_video_list)
            b_video_index = 0

            for i, a_video in enumerate(self.a_video_list):
                if self.is_cancelled:
                    break

                # 获取B视频（循环使用）
                b_video = self.b_video_list[b_video_index % len(self.b_video_list)]
                b_video_index += 1

                # 更新整体进度 - 开始处理当前视频
                overall_progress_start = int(i / total_videos * 100)
                self.progress_updated.emit(overall_progress_start, f"开始处理第{i+1}/{total_videos}个视频: {os.path.basename(a_video)}")

                # 处理视频对
                success = self.process_video_pair(a_video, b_video, i + 1)
                if not success:
                    self.process_finished.emit(False, f"处理第{i+1}个视频失败")
                    return

                # 更新整体进度 - 完成当前视频
                overall_progress_end = int((i + 1) / total_videos * 100)
                self.progress_updated.emit(overall_progress_end, f"完成第{i+1}/{total_videos}个视频: {os.path.basename(a_video)}")

            # 清理临时文件
            self.cleanup_temp_files()

            if not self.is_cancelled:
                self.process_finished.emit(True, f"成功处理 {total_videos} 个视频")

        except Exception as e:
            print(f"[释永信] 处理失败: {e}")
            self.cleanup_temp_files()
            self.process_finished.emit(False, str(e))

    def process_video_pair(self, a_video, b_video, pair_index):
        """处理单个视频对"""
        try:
            print(f"[释永信] 开始处理第{pair_index}个视频对")
            print(f"[释永信] A视频: {os.path.basename(a_video)}")
            print(f"[释永信] B视频: {os.path.basename(b_video)}")

            # 生成输出文件名：A视频文件名_疾风sph.mp4
            a_name = Path(a_video).stem
            output_filename = f"{a_name}_疾风sph.mp4"
            final_output = os.path.join(self.output_dir, output_filename)

            # 临时文件路径
            temp_video = os.path.join(self.temp_dir, f"temp_{pair_index}.mp4")
            temp_audio = os.path.join(self.temp_dir, f"temp_audio_{pair_index}.mp4")
            temp_dar = os.path.join(self.temp_dir, f"temp_dar_{pair_index}.mp4")

            # ========= 获取 A 时长/帧数 =========
            cap_a = cv2.VideoCapture(a_video)
            if not cap_a.isOpened():
                print(f"[释永信] 无法打开A视频: {a_video}")
                return False

            fps_a = cap_a.get(cv2.CAP_PROP_FPS) or self.FPS
            total_frames = int(cap_a.get(cv2.CAP_PROP_FRAME_COUNT))
            frames_out = int((total_frames / fps_a) * self.FPS)

            cap_b = cv2.VideoCapture(b_video)
            if not cap_b.isOpened():
                print(f"[释永信] 无法打开B视频: {b_video}")
                cap_a.release()
                return False

            # ========= 写 OpenCV 合成视频 =========
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            writer = cv2.VideoWriter(temp_video, fourcc, self.FPS, self.SIZE_OUT)
            if not writer.isOpened():
                print("[释永信] VideoWriter 无法打开！")
                cap_a.release()
                cap_b.release()
                return False

            print(f"[释永信] 开始 独家视频号引擎 合成 {frames_out} 帧…")
            mask_cache = {}
            frame_idx = 0

            while frame_idx < frames_out and not self.is_cancelled:
                ret_b, frame_b = cap_b.read()
                if not ret_b:
                    cap_b.set(cv2.CAP_PROP_POS_FRAMES, 0)
                    continue
                frame_b = self.resize_and_crop(frame_b, self.SIZE_BG)

                ret_a, frame_a = cap_a.read()
                if not ret_a:
                    break

                h_a, w_a = frame_a.shape[:2]
                scale = self.SIZE_BG / h_a
                new_w = int(w_a * scale)
                frame_a = cv2.resize(frame_a, (new_w, self.SIZE_BG), interpolation=cv2.INTER_LINEAR)

                if new_w not in mask_cache:
                    mask_cache[new_w] = self.make_mask(self.SIZE_BG, new_w, self.FEATHER)
                mask = mask_cache[new_w]
                mask_f = mask / 255.0
                inv = 1 - mask_f

                x = (self.SIZE_BG - new_w)//2
                roi = frame_b[:, x:x+new_w]
                blended = (roi * inv[...,None] + frame_a * mask_f[...,None]).astype(np.uint8)
                frame_b[:, x:x+new_w] = blended

                stretched = cv2.resize(frame_b, self.SIZE_OUT, interpolation=cv2.INTER_LINEAR)
                writer.write(stretched)

                frame_idx += 1

                # 实时更新进度 - 每10帧更新一次
                if frame_idx % 10 == 0 or frame_idx == frames_out:
                    # 计算OpenCV合成进度（占总进度的70%）
                    opencv_progress = int((frame_idx / frames_out) * 70)
                    self.progress_updated.emit(opencv_progress, f"OpenCV合成中: {frame_idx}/{frames_out} 帧 ({opencv_progress}%)")

                # 每100帧打印一次日志
                if frame_idx % 100 == 0:
                    print(f"[释永信] 已处理 {frame_idx}/{frames_out} 帧")

            cap_a.release()
            cap_b.release()
            writer.release()

            if self.is_cancelled:
                return False

            print("[释永信] temp视频画面合成完成")
            self.progress_updated.emit(70, "OpenCV合成完成，开始音频混流...")

            # ========= FFmpeg 混流音频 + DAR =========
            cmd_mix = [
                self.ffmpeg_path, "-y",
                "-i", temp_video,
                "-i", a_video,
                "-map", "0:v", "-map", "1:a?",
                "-c:v", "copy", "-c:a", "aac",
                "-aspect", "1:1",
                temp_audio
            ]
            print("[释永信] 调用 FFmpeg 混流音轨…")
            self.progress_updated.emit(75, "音频混流中...")
            result = subprocess.run(cmd_mix, capture_output=True, text=True)
            if result.returncode != 0:
                print(f"[释永信] FFmpeg混流失败: {result.stderr}")
                return False

            print("[释永信] 音频混流完成")
            self.progress_updated.emit(85, "音频混流完成，开始多轨封装...")

            # ========= 再执行一次极简 DAR 修正 =========
            cmd_dar = [
                self.ffmpeg_path, "-y",
                "-i", temp_audio,
                "-aspect", "1:1",
                temp_dar
            ]
            print("[释永信] 再次执行 DAR 修正命令…")
            self.progress_updated.emit(90, "DAR修正中...")
            result = subprocess.run(cmd_dar, capture_output=True, text=True)
            if result.returncode != 0:
                print(f"[释永信] DAR修正失败: {result.stderr}")
                return False

            # 移动到最终输出位置
            shutil.move(temp_dar, final_output)
            print(f"[释永信] 最终输出: {output_filename}")
            self.progress_updated.emit(95, f"文件输出完成: {output_filename}")

            # 删除使用过的B视频（如果启用）
            if self.delete_used_b and b_video not in self.used_b_videos:
                try:
                    os.remove(b_video)
                    self.used_b_videos.add(b_video)
                    print(f"[释永信] 已删除使用过的B视频: {os.path.basename(b_video)}")
                    self.progress_updated.emit(98, "清理B视频完成")
                except Exception as e:
                    print(f"[释永信] 删除B视频失败: {e}")

            self.progress_updated.emit(100, f"视频处理完成: {output_filename}")
            return True

        except Exception as e:
            print(f"[释永信] 处理视频对失败: {e}")
            return False
