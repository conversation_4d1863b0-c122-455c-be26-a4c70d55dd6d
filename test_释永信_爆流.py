#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试释永信-业内独创视频号爆流软件
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

def create_test_videos():
    """创建测试视频文件"""
    import cv2
    import numpy as np
    
    # 创建测试目录
    test_dir = "test_shiyongxin"
    a_dir = os.path.join(test_dir, "A_videos")
    b_dir = os.path.join(test_dir, "B_videos")
    output_dir = os.path.join(test_dir, "output")
    
    os.makedirs(a_dir, exist_ok=True)
    os.makedirs(b_dir, exist_ok=True)
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建测试A视频（主视频）
    for i in range(3):
        video_path = os.path.join(a_dir, f"test_a_{i+1}.mp4")
        create_test_video(video_path, duration=5+i*2, text=f"A视频{i+1}", color=(0, 255, 0))
    
    # 创建测试B视频（背景视频）
    for i in range(2):
        video_path = os.path.join(b_dir, f"test_b_{i+1}.mp4")
        create_test_video(video_path, duration=10, text=f"B视频{i+1}", color=(255, 0, 0))
    
    print(f"测试视频创建完成:")
    print(f"A视频目录: {a_dir}")
    print(f"B视频目录: {b_dir}")
    print(f"输出目录: {output_dir}")
    
    return a_dir, b_dir, output_dir

def create_test_video(output_path, duration=5, fps=30, text="Test", color=(255, 255, 255)):
    """创建测试视频"""
    import cv2
    import numpy as np

    # 使用更合适的尺寸，A视频使用16:9，B视频使用1:1
    if "test_a" in output_path:
        # A视频：16:9 横屏
        width, height = 1920, 1080
    else:
        # B视频：1:1 正方形
        width, height = 1080, 1080

    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

    total_frames = duration * fps

    for frame_idx in range(total_frames):
        # 创建彩色背景
        frame = np.full((height, width, 3), color, dtype=np.uint8)

        # 添加文字
        font = cv2.FONT_HERSHEY_SIMPLEX
        text_size = cv2.getTextSize(text, font, 3, 4)[0]
        text_x = (width - text_size[0]) // 2
        text_y = (height + text_size[1]) // 2

        cv2.putText(frame, text, (text_x, text_y), font, 3, (255, 255, 255), 4)
        cv2.putText(frame, f"Frame: {frame_idx}", (20, 60), font, 1, (255, 255, 255), 2)

        writer.write(frame)

    writer.release()
    print(f"创建测试视频: {output_path} ({duration}秒, {total_frames}帧, {width}x{height})")

def test_processor():
    """测试处理器功能"""
    try:
        # 检查FFmpeg
        ffmpeg_path = find_ffmpeg()
        if not ffmpeg_path:
            print("错误: 找不到FFmpeg")
            return False
        
        print(f"使用FFmpeg: {ffmpeg_path}")
        
        # 创建测试视频
        print("创建测试视频...")
        a_dir, b_dir, output_dir = create_test_videos()
        
        # 导入处理器
        from 释永信_业内独创视频号爆流 import ShiYongXinMultiProcessor
        
        # 获取视频列表
        a_videos = get_video_files(a_dir)
        b_videos = get_video_files(b_dir)
        
        print(f"A视频: {len(a_videos)} 个")
        print(f"B视频: {len(b_videos)} 个")
        
        if not a_videos or not b_videos:
            print("错误: 没有找到测试视频")
            return False
        
        # 创建处理器
        processor = ShiYongXinMultiProcessor(
            ffmpeg_path, a_videos, b_videos, output_dir, thread_count=2
        )
        
        # 连接信号
        def on_progress(value, message):
            print(f"进度: {value}% - {message}")
        
        def on_finished(success, message):
            print(f"完成: {'成功' if success else '失败'} - {message}")
        
        processor.progress_updated.connect(on_progress)
        processor.process_finished.connect(on_finished)
        
        # 开始处理
        print("开始处理...")
        processor.start()
        processor.wait()  # 等待完成
        
        # 检查输出文件
        output_files = [f for f in os.listdir(output_dir) if f.endswith('_开光成功.mp4')]
        print(f"生成的输出文件: {len(output_files)} 个")
        for f in output_files:
            print(f"  - {f}")
        
        return len(output_files) > 0
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def find_ffmpeg():
    """查找FFmpeg路径"""
    # 优先查找bin目录下的ffmpeg
    bin_ffmpeg = os.path.join(os.path.dirname(__file__), "bin", "ffmpeg.exe")
    if os.path.exists(bin_ffmpeg):
        return bin_ffmpeg
    
    # 查找当前目录
    current_ffmpeg = os.path.join(os.path.dirname(__file__), "ffmpeg.exe")
    if os.path.exists(current_ffmpeg):
        return current_ffmpeg
    
    # 检查系统PATH
    import subprocess
    try:
        result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True)
        if result.returncode == 0:
            return "ffmpeg"
    except:
        pass
    
    return None

def get_video_files(folder_path):
    """获取视频文件列表"""
    if not os.path.exists(folder_path):
        return []
    
    video_extensions = {'.mp4', '.avi', '.mov', '.mkv'}
    video_files = []
    
    for file in os.listdir(folder_path):
        file_path = os.path.join(folder_path, file)
        if os.path.isfile(file_path):
            _, ext = os.path.splitext(file.lower())
            if ext in video_extensions:
                video_files.append(file_path)
    
    return sorted(video_files)

def test_ui():
    """测试UI界面"""
    try:
        from PyQt5.QtWidgets import QApplication
        import sys
        
        app = QApplication(sys.argv)
        
        from 释永信_业内独创视频号爆流 import ShiYongXinMainWindow
        
        window = ShiYongXinMainWindow()
        window.show()
        
        print("UI界面启动成功")
        print("请手动测试界面功能...")
        
        # 不执行app.exec_()，只是显示窗口
        app.processEvents()
        
        return True
        
    except Exception as e:
        print(f"UI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("释永信-业内独创视频号爆流 测试程序")
    print("=" * 50)
    
    # 测试1: 处理器功能测试
    print("\n1. 测试处理器功能...")
    if test_processor():
        print("✅ 处理器功能测试通过")
    else:
        print("❌ 处理器功能测试失败")
    
    # 测试2: UI界面测试
    print("\n2. 测试UI界面...")
    if test_ui():
        print("✅ UI界面测试通过")
    else:
        print("❌ UI界面测试失败")
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
