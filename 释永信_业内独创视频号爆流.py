#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
释永信-业内独创视频号爆流
基于Codebase_shiyongxin.py的多线程视频处理软件
佛教风格UI设计
"""

import os
import sys
import cv2
import threading
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QLabel, QPushButton, QProgressBar, 
                             QFileDialog, QSpinBox, QTextEdit, QGroupBox,
                             QMessageBox, QFrame)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPalette, QColor, QIcon

# 导入释永信处理器
from Codebase_shiyongxin import WolverineProcessor

class ShiYongXinMultiProcessor(QThread):
    """释永信多线程处理器"""
    
    progress_updated = pyqtSignal(int, str)
    process_finished = pyqtSignal(bool, str)
    
    def __init__(self, ffmpeg_path, a_video_list, b_video_list, output_dir, thread_count=2):
        super().__init__()
        self.ffmpeg_path = ffmpeg_path
        self.a_video_list = a_video_list
        self.b_video_list = b_video_list
        self.output_dir = output_dir
        self.thread_count = thread_count
        self.is_cancelled = False
        self.longest_video_path = ""
        self.longest_video_duration = 0
        
        # 找到时长最长的A视频用于显示进度
        self.find_longest_video()
        
    def find_longest_video(self):
        """找到时长最长的A视频"""
        max_duration = 0
        longest_path = ""
        
        for video_path in self.a_video_list:
            try:
                cap = cv2.VideoCapture(video_path)
                if cap.isOpened():
                    fps = cap.get(cv2.CAP_PROP_FPS) or 30
                    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                    duration = frame_count / fps
                    
                    if duration > max_duration:
                        max_duration = duration
                        longest_path = video_path
                    
                    cap.release()
            except Exception as e:
                print(f"[释永信] 检测视频时长失败: {video_path} - {e}")
        
        self.longest_video_path = longest_path
        self.longest_video_duration = max_duration
        print(f"[释永信] 最长视频: {os.path.basename(longest_path)} ({max_duration:.1f}秒)")
    
    def cancel(self):
        """取消处理"""
        self.is_cancelled = True
        print("[释永信] 多线程处理已取消")
    
    def run(self):
        """多线程处理主流程"""
        try:
            if not self.a_video_list:
                self.process_finished.emit(False, "A视频列表为空")
                return
                
            if not self.b_video_list:
                self.process_finished.emit(False, "B视频列表为空")
                return
            
            total_videos = len(self.a_video_list)
            completed_videos = 0
            failed_videos = 0
            
            # 创建线程池
            with ThreadPoolExecutor(max_workers=self.thread_count) as executor:
                # 提交所有任务
                future_to_video = {}
                b_video_index = 0
                
                for i, a_video in enumerate(self.a_video_list):
                    if self.is_cancelled:
                        break
                    
                    # 循环使用B视频
                    b_video = self.b_video_list[b_video_index % len(self.b_video_list)]
                    b_video_index += 1
                    
                    # 提交处理任务
                    future = executor.submit(self.process_single_video, a_video, b_video, i + 1)
                    future_to_video[future] = (a_video, b_video, i + 1)
                
                # 处理完成的任务
                for future in as_completed(future_to_video):
                    if self.is_cancelled:
                        break
                    
                    a_video, b_video, index = future_to_video[future]
                    
                    try:
                        success = future.result()
                        if success:
                            completed_videos += 1
                        else:
                            failed_videos += 1
                    except Exception as e:
                        print(f"[释永信] 处理视频异常: {os.path.basename(a_video)} - {e}")
                        failed_videos += 1
                    
                    # 更新整体进度
                    progress = int((completed_videos + failed_videos) / total_videos * 100)
                    status_msg = f"已完成: {completed_videos}/{total_videos} (失败: {failed_videos})"
                    self.progress_updated.emit(progress, status_msg)
            
            if not self.is_cancelled:
                if failed_videos == 0:
                    self.process_finished.emit(True, f"开光成功！共处理 {completed_videos} 个视频")
                else:
                    self.process_finished.emit(True, f"处理完成：成功 {completed_videos} 个，失败 {failed_videos} 个")
            
        except Exception as e:
            print(f"[释永信] 多线程处理失败: {e}")
            self.process_finished.emit(False, f"处理失败: {str(e)}")
    
    def process_single_video(self, a_video, b_video, index):
        """处理单个视频"""
        try:
            print(f"[释永信] 线程 {threading.current_thread().name} 开始处理第{index}个视频")

            # 生成输出文件名：A视频文件名_开光成功.mp4
            a_name = Path(a_video).stem
            output_filename = f"{a_name}_开光成功.mp4"
            final_output = os.path.join(self.output_dir, output_filename)

            # 创建临时输出目录用于单个处理器
            import tempfile
            temp_output_dir = tempfile.mkdtemp(prefix="shiyongxin_")

            try:
                # 创建单个处理器实例
                processor = WolverineProcessor(
                    self.ffmpeg_path, [a_video], [b_video],
                    temp_output_dir, "开光成功", False
                )

                # 手动设置temp_dir，因为我们不调用run方法
                processor.temp_dir = tempfile.mkdtemp(prefix="wolverine_")

                # 如果是最长视频，连接进度信号
                if a_video == self.longest_video_path:
                    processor.progress_updated.connect(self.progress_updated.emit)

                # 直接调用处理方法
                success = processor.process_video_pair(a_video, b_video, index)

                if success:
                    # 查找生成的文件并移动到最终位置
                    temp_output = os.path.join(temp_output_dir, f"{a_name}_疾风sph.mp4")
                    if os.path.exists(temp_output):
                        import shutil
                        shutil.move(temp_output, final_output)
                        print(f"[释永信] 开光成功: {output_filename}")
                    else:
                        print(f"[释永信] 警告: 未找到临时输出文件 {temp_output}")
                        success = False

                # 清理处理器的临时目录
                if processor.temp_dir and os.path.exists(processor.temp_dir):
                    try:
                        import shutil
                        shutil.rmtree(processor.temp_dir)
                    except Exception as e:
                        print(f"[释永信] 清理处理器临时目录失败: {e}")

                return success

            finally:
                # 清理临时目录
                try:
                    import shutil
                    shutil.rmtree(temp_output_dir)
                except Exception as e:
                    print(f"[释永信] 清理临时目录失败: {e}")

        except Exception as e:
            print(f"[释永信] 处理单个视频失败: {os.path.basename(a_video)} - {e}")
            return False


class ShiYongXinMainWindow(QMainWindow):
    """释永信-业内独创视频号爆流 主窗口"""
    
    def __init__(self):
        super().__init__()
        self.ffmpeg_path = self.find_ffmpeg()
        self.a_video_folder = ""
        self.b_video_folder = ""
        self.a_video_list = []
        self.b_video_list = []
        self.output_dir = ""
        self.is_processing = False
        self.worker = None
        
        self.setup_ui()
        self.apply_buddhist_style()
    
    def find_ffmpeg(self):
        """查找FFmpeg路径"""
        # 优先查找bin目录下的ffmpeg
        bin_ffmpeg = os.path.join(os.path.dirname(__file__), "bin", "ffmpeg.exe")
        if os.path.exists(bin_ffmpeg):
            return bin_ffmpeg
        
        # 查找当前目录
        current_ffmpeg = os.path.join(os.path.dirname(__file__), "ffmpeg.exe")
        if os.path.exists(current_ffmpeg):
            return current_ffmpeg
        
        return "ffmpeg"  # 系统PATH中的ffmpeg
    
    def setup_ui(self):
        """设置UI界面"""
        self.setWindowTitle("释永信-业内独创视频号爆流 | 南无阿弥陀佛")
        self.setGeometry(100, 100, 1200, 700)  # 调整为横版尺寸

        # 创建中央窗口
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局 - 改为水平布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # 左侧区域 - 控制面板
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setSpacing(15)

        # 标题区域
        title_frame = QFrame()
        title_frame.setObjectName("title_frame")
        title_layout = QVBoxLayout(title_frame)

        title_label = QLabel("释永信-业内独创视频号爆流")
        title_label.setObjectName("main_title")
        title_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(title_label)

        subtitle_label = QLabel("南无阿弥陀佛 | 功德无量 | 视频开光")
        subtitle_label.setObjectName("subtitle")
        subtitle_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(subtitle_label)

        left_layout.addWidget(title_frame)
        
        # 文件选择区域
        file_group = QGroupBox("📁 选择视频功德箱")
        file_group.setObjectName("file_group")
        file_layout = QVBoxLayout(file_group)

        # A视频功德箱
        a_layout = QVBoxLayout()
        a_label_text = QLabel("A视频功德箱 (主视频):")
        a_label_text.setObjectName("label_text")
        self.a_label = QLabel("未选择")
        self.a_label.setObjectName("folder_label")
        a_btn = QPushButton("选择A视频功德箱")
        a_btn.setObjectName("folder_btn")
        a_btn.clicked.connect(self.select_a_video_folder)
        a_layout.addWidget(a_label_text)
        a_layout.addWidget(self.a_label)
        a_layout.addWidget(a_btn)
        file_layout.addLayout(a_layout)

        # B视频功德箱
        b_layout = QVBoxLayout()
        b_label_text = QLabel("B视频功德箱 (背景视频):")
        b_label_text.setObjectName("label_text")
        self.b_label = QLabel("未选择")
        self.b_label.setObjectName("folder_label")
        b_btn = QPushButton("选择B视频功德箱")
        b_btn.setObjectName("folder_btn")
        b_btn.clicked.connect(self.select_b_video_folder)
        b_layout.addWidget(b_label_text)
        b_layout.addWidget(self.b_label)
        b_layout.addWidget(b_btn)
        file_layout.addLayout(b_layout)

        # 输出目录
        output_layout = QVBoxLayout()
        output_label_text = QLabel("输出目录:")
        output_label_text.setObjectName("label_text")
        self.output_label = QLabel("默认为A视频所在功德箱")
        self.output_label.setObjectName("folder_label")
        output_btn = QPushButton("选择输出目录")
        output_btn.setObjectName("folder_btn")
        output_btn.clicked.connect(self.select_output_dir)
        output_layout.addWidget(output_label_text)
        output_layout.addWidget(self.output_label)
        output_layout.addWidget(output_btn)
        file_layout.addLayout(output_layout)

        left_layout.addWidget(file_group)
        
        # 处理设置区域
        settings_group = QGroupBox("⚙️ 处理设置")
        settings_group.setObjectName("settings_group")
        settings_layout = QHBoxLayout(settings_group)
        
        thread_label = QLabel("线程数量:")
        thread_label.setObjectName("setting_label")
        self.thread_spinbox = QSpinBox()
        self.thread_spinbox.setRange(1, 8)
        self.thread_spinbox.setValue(2)
        self.thread_spinbox.setObjectName("thread_spinbox")
        
        settings_layout.addWidget(thread_label)
        settings_layout.addWidget(self.thread_spinbox)
        settings_layout.addStretch()
        
        main_layout.addWidget(settings_group)
        
        # 控制按钮区域
        control_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("🙏 开始开光")
        self.start_btn.setObjectName("start_btn")
        self.start_btn.clicked.connect(self.start_processing)
        
        self.stop_btn = QPushButton("⏹️ 停止处理")
        self.stop_btn.setObjectName("stop_btn")
        self.stop_btn.clicked.connect(self.stop_processing)
        self.stop_btn.setEnabled(False)
        
        control_layout.addWidget(self.start_btn)
        control_layout.addWidget(self.stop_btn)
        
        main_layout.addLayout(control_layout)
        
        # 进度显示区域
        progress_group = QGroupBox("📊 处理进度")
        progress_group.setObjectName("progress_group")
        progress_layout = QVBoxLayout(progress_group)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setObjectName("progress_bar")
        self.progress_bar.setValue(0)
        
        self.status_label = QLabel("准备就绪，等待开光...")
        self.status_label.setObjectName("status_label")
        self.status_label.setAlignment(Qt.AlignCenter)
        
        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.status_label)
        
        main_layout.addWidget(progress_group)
        
        # 日志显示区域
        log_group = QGroupBox("📝 处理日志")
        log_group.setObjectName("log_group")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setObjectName("log_text")
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        
        log_layout.addWidget(self.log_text)
        main_layout.addWidget(log_group)
        
        # 底部版权信息
        copyright_label = QLabel("© 释永信工作室 | 业内独创技术 | 功德回向众生")
        copyright_label.setObjectName("copyright")
        copyright_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(copyright_label)

    def apply_buddhist_style(self):
        """应用佛教风格样式"""
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #8B4513, stop:0.5 #CD853F, stop:1 #DEB887);
            }

            #title_frame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FFD700, stop:1 #FFA500);
                border: 3px solid #8B4513;
                border-radius: 15px;
                padding: 20px;
                margin: 10px;
            }

            #main_title {
                font-family: "Microsoft YaHei", "SimHei";
                font-size: 28px;
                font-weight: bold;
                color: #8B0000;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            }

            #subtitle {
                font-family: "Microsoft YaHei", "KaiTi";
                font-size: 16px;
                color: #8B4513;
                font-style: italic;
                margin-top: 10px;
            }

            QGroupBox {
                font-family: "Microsoft YaHei";
                font-size: 14px;
                font-weight: bold;
                color: #8B4513;
                border: 2px solid #CD853F;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 15px;
                background: rgba(255, 255, 255, 0.1);
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 10px 0 10px;
                color: #8B0000;
                font-weight: bold;
            }

            #folder_label {
                font-family: "Microsoft YaHei";
                font-size: 12px;
                color: #2F4F4F;
                background: rgba(255, 255, 255, 0.8);
                padding: 8px;
                border-radius: 5px;
                border: 1px solid #CD853F;
            }

            #folder_btn, #start_btn, #stop_btn {
                font-family: "Microsoft YaHei";
                font-size: 12px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #DAA520, stop:1 #B8860B);
                border: 2px solid #8B4513;
                border-radius: 8px;
                padding: 10px 20px;
                min-width: 120px;
            }

            #folder_btn:hover, #start_btn:hover, #stop_btn:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FFD700, stop:1 #DAA520);
            }

            #start_btn {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FF6347, stop:1 #DC143C);
                font-size: 16px;
                min-width: 150px;
                min-height: 50px;
            }

            #start_btn:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FF7F50, stop:1 #FF6347);
            }

            #stop_btn {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #696969, stop:1 #2F4F4F);
            }

            #setting_label {
                font-family: "Microsoft YaHei";
                font-size: 12px;
                color: #8B4513;
                font-weight: bold;
            }

            #thread_spinbox {
                font-family: "Microsoft YaHei";
                font-size: 12px;
                padding: 5px;
                border: 2px solid #CD853F;
                border-radius: 5px;
                background: white;
                min-width: 60px;
            }

            #progress_bar {
                border: 2px solid #8B4513;
                border-radius: 8px;
                background: #F5F5DC;
                text-align: center;
                font-weight: bold;
                color: #8B4513;
            }

            #progress_bar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #FFD700, stop:0.5 #FFA500, stop:1 #FF8C00);
                border-radius: 6px;
            }

            #status_label {
                font-family: "Microsoft YaHei";
                font-size: 14px;
                color: #8B4513;
                font-weight: bold;
                background: rgba(255, 255, 255, 0.8);
                padding: 10px;
                border-radius: 5px;
                border: 1px solid #CD853F;
            }

            #log_text {
                font-family: "Consolas", "Microsoft YaHei";
                font-size: 10px;
                background: #F5F5DC;
                border: 2px solid #CD853F;
                border-radius: 5px;
                color: #2F4F4F;
            }

            #copyright {
                font-family: "KaiTi", "Microsoft YaHei";
                font-size: 12px;
                color: #8B4513;
                font-style: italic;
                background: rgba(255, 255, 255, 0.3);
                padding: 8px;
                border-radius: 5px;
                margin-top: 10px;
            }
        """)

    def log_message(self, message):
        """添加日志消息"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        self.log_text.verticalScrollBar().setValue(
            self.log_text.verticalScrollBar().maximum()
        )

    def get_video_files_from_folder(self, folder_path):
        """从功德箱获取视频文件列表"""
        if not folder_path or not os.path.exists(folder_path):
            return []

        # 支持的视频格式
        video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v'}
        video_files = []

        try:
            for file in os.listdir(folder_path):
                file_path = os.path.join(folder_path, file)
                if os.path.isfile(file_path):
                    _, ext = os.path.splitext(file.lower())
                    if ext in video_extensions:
                        video_files.append(file_path)

            # 按文件名排序
            video_files.sort()
            return video_files

        except Exception as e:
            self.log_message(f"扫描视频文件失败: {e}")
            return []

    def select_a_video_folder(self):
        """选择A视频功德箱"""
        folder_path = QFileDialog.getExistingDirectory(self, "选择A视频功德箱（主视频）")
        if folder_path:
            self.a_video_folder = folder_path
            self.a_video_list = self.get_video_files_from_folder(folder_path)

            folder_name = os.path.basename(folder_path)
            video_count = len(self.a_video_list)

            if video_count > 0:
                self.a_label.setText(f"A视频功德箱: {folder_name} ({video_count} 个视频)")
                self.a_label.setStyleSheet(self.a_label.styleSheet() + "color: #006400;")
                self.log_message(f"选择A视频功德箱: {folder_name}，找到 {video_count} 个视频")

                # 默认设置输出目录为A视频所在功德箱
                if not self.output_dir:
                    self.output_dir = folder_path
                    self.output_label.setText(f"输出目录: {folder_name}")
                    self.output_label.setStyleSheet(self.output_label.styleSheet() + "color: #006400;")
            else:
                self.a_label.setText(f"A视频功德箱: {folder_name} (无视频文件)")
                self.a_label.setStyleSheet(self.a_label.styleSheet() + "color: #DC143C;")
                QMessageBox.warning(self, "警告", "选择的功德箱中没有找到视频文件")
                self.log_message(f"警告: {folder_name} 功德箱中无视频文件")

    def select_b_video_folder(self):
        """选择B视频功德箱"""
        folder_path = QFileDialog.getExistingDirectory(self, "选择B视频功德箱（背景视频）")
        if folder_path:
            self.b_video_folder = folder_path
            self.b_video_list = self.get_video_files_from_folder(folder_path)

            folder_name = os.path.basename(folder_path)
            video_count = len(self.b_video_list)

            if video_count > 0:
                self.b_label.setText(f"B视频功德箱: {folder_name} ({video_count} 个视频)")
                self.b_label.setStyleSheet(self.b_label.styleSheet() + "color: #006400;")
                self.log_message(f"选择B视频功德箱: {folder_name}，找到 {video_count} 个视频")
            else:
                self.b_label.setText(f"B视频功德箱: {folder_name} (无视频文件)")
                self.b_label.setStyleSheet(self.b_label.styleSheet() + "color: #DC143C;")
                QMessageBox.warning(self, "警告", "选择的功德箱中没有找到视频文件")
                self.log_message(f"警告: {folder_name} 功德箱中无视频文件")

    def select_output_dir(self):
        """选择输出目录"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if dir_path:
            self.output_dir = dir_path
            dir_name = os.path.basename(dir_path)
            self.output_label.setText(f"输出目录: {dir_name}")
            self.output_label.setStyleSheet(self.output_label.styleSheet() + "color: #006400;")
            self.log_message(f"选择输出目录: {dir_name}")

    def start_processing(self):
        """开始处理"""
        # 验证输入
        if not self.a_video_list:
            QMessageBox.warning(self, "警告", "请先选择A视频功德箱")
            return

        if not self.b_video_list:
            QMessageBox.warning(self, "警告", "请先选择B视频功德箱")
            return

        if not self.output_dir:
            QMessageBox.warning(self, "警告", "请先选择输出目录")
            return

        if not os.path.exists(self.ffmpeg_path) and self.ffmpeg_path != "ffmpeg":
            QMessageBox.critical(self, "错误", f"找不到FFmpeg: {self.ffmpeg_path}")
            return

        # 设置UI状态
        self.is_processing = True
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("正在准备开光...")

        # 获取线程数
        thread_count = self.thread_spinbox.value()

        self.log_message(f"开始开光处理，使用 {thread_count} 个线程")
        self.log_message(f"A视频: {len(self.a_video_list)} 个")
        self.log_message(f"B视频: {len(self.b_video_list)} 个")
        self.log_message(f"输出目录: {self.output_dir}")

        # 创建多线程处理器
        self.worker = ShiYongXinMultiProcessor(
            self.ffmpeg_path, self.a_video_list, self.b_video_list,
            self.output_dir, thread_count
        )

        # 连接信号
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.process_finished.connect(self.on_process_finished)

        # 启动处理
        self.worker.start()

    def stop_processing(self):
        """停止处理"""
        if self.worker:
            self.worker.cancel()
            self.worker.wait()

        self.is_processing = False
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.status_label.setText("处理已停止")
        self.log_message("用户停止了处理")

    def update_progress(self, value, message):
        """更新进度"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)
        if "开光成功" in message or "处理完成" in message:
            self.log_message(message)

    def on_process_finished(self, success, message):
        """处理完成"""
        self.is_processing = False
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)

        if success:
            self.progress_bar.setValue(100)
            self.status_label.setText("🎉 " + message)
            self.log_message(f"处理完成: {message}")
            QMessageBox.information(self, "开光成功", f"南无阿弥陀佛！\n\n{message}")
        else:
            self.status_label.setText("❌ " + message)
            self.log_message(f"处理失败: {message}")
            QMessageBox.critical(self, "开光失败", f"处理失败:\n{message}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')

    # 设置全局字体
    font = QFont("Microsoft YaHei", 10)
    app.setFont(font)

    # 创建主窗口
    window = ShiYongXinMainWindow()
    window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
